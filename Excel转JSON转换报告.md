# Excel转JSON转换报告

## 转换概述
成功将 `location_data.xlsx.remove.xlsx` 文件的三个sheet转换为JSON格式文件。

## 源文件信息
- **文件名**: location_data.xlsx.remove.xlsx
- **Sheet数量**: 3个
- **Sheet名称**: Sheet1, Sheet2, Sheet3

## 转换结果

### 1. 国家列表 (Sheet1 → location_data_country.json)
- **记录数量**: 207条
- **文件大小**: 21KB
- **字段结构**:
  - `text`: 国家名称
  - `code`: 国家编码
  - **flagImg**: 国家对应的图标URL

**示例数据**:
```json
{"text": "Australia", "code": "AU", "flagImg": "https://libcdn1.hahbuy.com/prod/base/country/au.png"}
```

### 2. 省级行政区划 (Sheet2 → location_data_province.json)
- **记录数量**: 5,077条
- **文件大小**: 1.3MB
- **字段结构**:
  - `id`: ID
  - `pid`: 省编码
  - `nameCn`: 中文名
  - `nameEn`: 英文名
  - `nameLocal`: 本地名
  - `createBy`: 创建人
  - `createTime`: 创建时间
  - `updateBy`: 更新人
  - `updateTime`: 更新时间
  - `hasChild`: 是否有子级
  - `postalCode`: 邮政编码
  - `country_code`: 国家编码
  - `country_name`: 国家名称

**示例数据**:
```json
{"id": null, "pid": "3907", "nameCn": "Australian Capital Territory", "nameEn": "Australian Capital Territory", "nameLocal": "Australian Capital Territory", "createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "hasChild": null, "postalCode": null, "country_code": "AU", "country_name": "Australia"}
```

### 3. 市级行政区划 (Sheet3 → location_data_city.json)
- **记录数量**: 164,311条
- **文件大小**: 49MB
- **字段结构**:
  - `id`: ID
  - `pid`: PID
  - `nameCn`: 中文名
  - `nameEn`: 英文名
  - `nameLocal`: 本地名
  - `createBy`: 创建人
  - `createTime`: 创建时间
  - `updateBy`: 更新人
  - `updateTime`: 更新时间
  - `hasChild`: 是否有子级
  - `postalCode`: 邮政编码
  - `state_pid`: 省编码
  - `state_name`: 省名称
  - `country_code`: 国家编码
  - `country_name`: 国家名称

**示例数据**:
```json
{"id": "3915", "pid": null, "nameCn": "Acton", "nameEn": "Acton", "nameLocal": "Acton", "createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "hasChild": null, "postalCode": null, "state_pid": "3907", "state_name": null, "country_code": "AU", "country_name": "Australia"}
```

## 技术实现

### 使用的工具和库
- **Python 3.9.6**
- **openpyxl**: 用于读取Excel文件
- **json**: 用于JSON格式化输出

### 转换脚本
- **脚本名称**: `convert_excel_to_json.py`
- **输出格式**: 每行一条JSON记录 (JSONL格式)
- **编码**: UTF-8
- **特殊处理**: 
  - 自动处理空值 (None)
  - 日期时间格式化为字符串
  - 保持原始数据类型

## 输出文件

### 生成的JSON文件
1. **location_data_country.json** - 国家列表数据
2. **location_data_province.json** - 省级行政区划数据
3. **location_data_city.json** - 市级行政区划数据

### 文件特点
- **格式**: JSONL (每行一个JSON对象)
- **编码**: UTF-8
- **字段**: 保持原Excel表头字段名
- **数据完整性**: 完全保留原始数据，包括空值

## 数据统计

| 数据类型 | 记录数量 | 文件大小 | 主要用途 |
|---------|---------|---------|---------|
| 国家 | 207 | 21KB | 国家选择器 |
| 省/州 | 5,077 | 1.3MB | 省级地址选择 |
| 市/区 | 164,311 | 49MB | 城市地址选择 |

## 使用建议

### 1. 数据导入
```bash
# 逐行读取JSON文件
while IFS= read -r line; do
    echo "$line" | jq '.'
done < location_data_country.json
```

### 2. 数据库导入
可以使用各种工具将JSONL格式数据导入数据库：
- MongoDB: `mongoimport`
- PostgreSQL: `COPY` 命令
- MySQL: 自定义脚本

### 3. 程序中使用
```python
import json

# 读取国家数据
countries = []
with open('location_data_country.json', 'r', encoding='utf-8') as f:
    for line in f:
        countries.append(json.loads(line))
```

## 转换完成时间
**2025-07-24 17:34**

转换过程顺利完成，所有数据已成功从Excel格式转换为JSON格式，可以直接用于后续的开发和数据处理工作。

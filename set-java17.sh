#!/bin/bash
# 设置Java 17环境变量并执行Maven命令
export JAVA_HOME=/Users/<USER>/openjdk/jdk-17.0.2.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH

echo "Java 17 environment set:"
java -version
echo ""

# 如果有参数，执行Maven命令
if [ $# -gt 0 ]; then
    echo "Executing: mvn $@"
    mvn "$@"
else
    echo "Usage: ./set-java17.sh [maven-goals]"
    echo "Examples:"
    echo "  ./set-java17.sh clean compile"
    echo "  ./set-java17.sh clean package -DskipTests"
    echo "  ./set-java17.sh clean package deploy -DskipTests"
fi

-- 地址管理表结构设计

-- 地址表（统一存储国家、省、市三级地址信息）
CREATE TABLE `address` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `code` VARCHAR(50) NOT NULL COMMENT '地址编码（国家编码/省编码/市编码）',
    `name` VARCHAR(255) NOT NULL COMMENT '地址名称（国家名称/省市名称）',
    `name_cn` VARCHAR(255) COMMENT '中文名称',
    `name_en` VARCHAR(255) COMMENT '英文名称',
    `name_local` VARCHAR(255) COMMENT '本地名称',
    `level` TINYINT NOT NULL COMMENT '地址级别：1-国家，2-省/州，3-市/区',
    `parent_code` VARCHAR(50) COMMENT '父级地址编码',
    `country_code` VARCHAR(10) COMMENT '所属国家编码',
    `flag_img` VARCHAR(500) COMMENT '国家图标URL（仅国家级别使用）',
    `postal_code` VARCHAR(20) COMMENT '邮政编码',
    `has_child` TINYINT DEFAULT 0 COMMENT '是否有子级：0-否，1-是',
    `create_by` VARCHAR(100) COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` VARCHAR(100) COMMENT '更新人',
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    INDEX `idx_code` (`code`),
    INDEX `idx_level` (`level`),
    INDEX `idx_parent_code` (`parent_code`),
    INDEX `idx_country_code` (`country_code`),
    INDEX `idx_level_parent` (`level`, `parent_code`),
    INDEX `idx_country_level` (`country_code`, `level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地址管理表';

-- 创建索引优化查询性能
-- 查询所有国家
-- 根据国家编码查询省级行政区划
-- 根据省编码查询市级行政区划

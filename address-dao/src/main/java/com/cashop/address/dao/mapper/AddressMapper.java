package com.cashop.address.dao.mapper;

import com.cashop.address.common.entity.Address;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地址数据访问接口
 *
 * <AUTHOR>
 */
@Mapper
public interface AddressMapper {

    /**
     * 批量插入地址数据
     *
     * @param addresses 地址列表
     * @return 插入数量
     */
    int batchInsert(@Param("addresses") List<Address> addresses);

    /**
     * 根据ID查询地址
     *
     * @param id 地址ID
     * @return 地址信息
     */
    Address selectById(@Param("id") Long id);

    /**
     * 根据编码查询地址
     *
     * @param code 地址编码
     * @return 地址信息
     */
    Address selectByCode(@Param("code") String code);

    /**
     * 查询所有国家列表
     *
     * @return 国家列表
     */
    List<Address> selectAllCountries();

    /**
     * 根据国家编码查询省级行政区划列表
     *
     * @param countryCode 国家编码
     * @return 省级行政区划列表
     */
    List<Address> selectProvincesByCountryCode(@Param("countryCode") String countryCode);

    /**
     * 根据省编码查询市级行政区划列表
     *
     * @param provinceCode 省编码
     * @return 市级行政区划列表
     */
    List<Address> selectCitiesByProvinceCode(@Param("provinceCode") String provinceCode);

    /**
     * 根据级别和父级编码查询地址列表
     *
     * @param level      地址级别
     * @param parentCode 父级编码
     * @return 地址列表
     */
    List<Address> selectByLevelAndParentCode(@Param("level") Integer level, @Param("parentCode") String parentCode);

    /**
     * 根据级别查询地址列表
     *
     * @param level 地址级别
     * @return 地址列表
     */
    List<Address> selectByLevel(@Param("level") Integer level);

    /**
     * 更新地址信息
     *
     * @param address 地址信息
     * @return 更新数量
     */
    int updateById(Address address);

    /**
     * 根据ID删除地址
     *
     * @param id 地址ID
     * @return 删除数量
     */
    int deleteById(@Param("id") Long id);

    /**
     * 统计地址数量
     *
     * @param level       地址级别
     * @param countryCode 国家编码
     * @return 地址数量
     */
    long countByLevelAndCountryCode(@Param("level") Integer level, @Param("countryCode") String countryCode);
}

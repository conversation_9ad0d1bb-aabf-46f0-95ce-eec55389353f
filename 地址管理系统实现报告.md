# 地址管理系统实现报告

## 项目概述
基于Spring Boot 3.2.6 + MyBatis的地址管理系统，实现了国家、省、市三级地址数据的统一管理和RESTful API接口。

## 数据源分析
### 原始数据文件
1. **location_data_country.json** - 国家列表（207条记录）
   - `text`: 国家名称
   - `code`: 国家编码  
   - `flagImg`: 国家图标URL

2. **location_data_province.json** - 省级行政区划（5,077条记录）
   - `pid`: 省编码
   - `nameCn`: 中文名
   - `nameEn`: 英文名
   - `nameLocal`: 本地名
   - `country_code`: 国家编码

3. **location_data_city.json** - 市级行政区划（164,311条记录）
   - `id`: 市编码
   - `nameCn`: 中文名
   - `nameEn`: 英文名
   - `nameLocal`: 本地名
   - `state_pid`: 省编码

## 数据库设计

### 地址表结构 (address)
```sql
CREATE TABLE `address` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(50) NOT NULL COMMENT '地址编码',
    `name` VARCHAR(255) NOT NULL COMMENT '地址名称',
    `name_cn` VARCHAR(255) COMMENT '中文名称',
    `name_en` VARCHAR(255) COMMENT '英文名称', 
    `name_local` VARCHAR(255) COMMENT '本地名称',
    `level` TINYINT NOT NULL COMMENT '地址级别：1-国家，2-省，3-市',
    `parent_code` VARCHAR(50) COMMENT '父级地址编码',
    `country_code` VARCHAR(10) COMMENT '所属国家编码',
    `flag_img` VARCHAR(500) COMMENT '国家图标URL',
    `postal_code` VARCHAR(20) COMMENT '邮政编码',
    `has_child` TINYINT DEFAULT 0 COMMENT '是否有子级',
    `create_by` VARCHAR(100) COMMENT '创建人',
    `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
    `update_by` VARCHAR(100) COMMENT '更新人', 
    `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-启用'
);
```

### 索引设计
- `idx_code`: 地址编码索引
- `idx_level`: 地址级别索引
- `idx_parent_code`: 父级编码索引
- `idx_country_code`: 国家编码索引
- `idx_level_parent`: 级别+父级编码复合索引
- `idx_country_level`: 国家+级别复合索引

## 系统架构

### 模块结构
```
address/
├── address-common/      # 公共模块：实体类、工具类
├── address-dao/         # 数据访问层：MyBatis Mapper
├── address-facade/      # 外观层：Feign客户端
├── address-service/     # 业务服务层：业务逻辑
└── address-web/         # Web控制层：RESTful API
```

### 核心组件

#### 1. 实体类 (Address.java)
- 统一的地址实体，支持三级地址数据
- 包含完整的字段映射和getter/setter方法

#### 2. 数据访问层 (AddressMapper)
- 批量插入接口：`batchInsert`
- 查询接口：按级别、编码、父级编码查询
- 统计接口：`countByLevelAndCountryCode`

#### 3. 业务服务层 (AddressService)
- 数据初始化：`initAddressData`
- 查询服务：国家、省、市三级查询
- 数据统计：地址数量统计

#### 4. Web控制层 (AddressController)
- RESTful API接口
- 完整的Swagger文档注解
- 统一的响应格式

## RESTful API接口

### 1. 数据初始化
```
POST /api/address/init
功能：从JSON文件导入地址数据到数据库
响应：{"success": true, "message": "导入结果", "code": 200}
```

### 2. 查询所有国家列表
```
GET /api/address/countries
功能：获取所有可用的国家列表
响应：{"success": true, "data": [...], "total": 207, "code": 200}
```

### 3. 根据国家编码查询省级行政区划
```
GET /api/address/provinces?countryCode=US
功能：根据国家编码查询该国家下的所有省/州
参数：countryCode - 国家编码（必填）
响应：{"success": true, "data": [...], "total": N, "code": 200}
```

### 4. 根据省编码查询市级行政区划
```
GET /api/address/cities?provinceCode=3907
功能：根据省编码查询该省下的所有市/区
参数：provinceCode - 省编码（必填）
响应：{"success": true, "data": [...], "total": N, "code": 200}
```

### 5. 根据编码查询地址详情
```
GET /api/address/detail?code=US
功能：根据地址编码查询具体的地址信息
参数：code - 地址编码（必填）
响应：{"success": true, "data": {...}, "code": 200}
```

### 6. 统计地址数量
```
GET /api/address/count?level=1&countryCode=US
功能：统计指定级别和国家的地址数量
参数：
- level - 地址级别：1-国家，2-省，3-市（必填）
- countryCode - 国家编码（可选）
响应：{"success": true, "count": N, "code": 200}
```

## 数据映射规则

### 国家数据映射
- `code` → `code` (地址编码)
- `text` → `name` (地址名称)
- `text` → `nameEn` (英文名称)
- `flagImg` → `flagImg` (国家图标)
- `level` = 1 (国家级别)
- `countryCode` = `code` (国家编码)

### 省级数据映射
- `pid` → `code` (地址编码)
- `nameEn` → `name` (地址名称)
- `nameCn` → `nameCn` (中文名称)
- `nameEn` → `nameEn` (英文名称)
- `nameLocal` → `nameLocal` (本地名称)
- `level` = 2 (省级别)
- `country_code` → `countryCode` (国家编码)

### 市级数据映射
- `id` → `code` (地址编码)
- `nameEn` → `name` (地址名称)
- `nameCn` → `nameCn` (中文名称)
- `nameEn` → `nameEn` (英文名称)
- `nameLocal` → `nameLocal` (本地名称)
- `level` = 3 (市级别)
- `state_pid` → `parentCode` (父级编码)
- `country_code` → `countryCode` (国家编码)

## 技术特性

### 1. 批量数据处理
- 分批插入，每批1000条记录
- 事务管理，确保数据一致性
- 异常处理和回滚机制

### 2. 性能优化
- 多级索引设计
- 分页查询支持
- 连接池配置优化

### 3. 数据完整性
- 外键关系维护
- 状态字段管理
- 创建和更新时间自动维护

## 部署说明

### 1. 环境要求
- JDK 17
- MySQL 8.0+
- Spring Boot 3.2.6

### 2. 数据库初始化
```sql
-- 执行建表脚本
source create_table.sql;
```

### 3. 应用启动
```bash
cd address-web
mvn spring-boot:run -Dspring-boot.run.profiles=local
```

### 4. 数据导入
```bash
curl -X POST http://localhost:8080/api/address/init
```

## API文档访问
- **Swagger UI**: http://localhost:8080/swagger-ui/index.html
- **API文档**: http://localhost:8080/v3/api-docs

## 使用示例

### 1. 获取所有国家
```bash
curl -X GET http://localhost:8080/api/address/countries
```

### 2. 获取美国的州
```bash
curl -X GET "http://localhost:8080/api/address/provinces?countryCode=US"
```

### 3. 获取某省的城市
```bash
curl -X GET "http://localhost:8080/api/address/cities?provinceCode=3907"
```

## 总结
成功实现了完整的地址管理系统，包括：
- ✅ 数据库表设计和创建
- ✅ 三级地址数据统一管理
- ✅ RESTful API接口实现
- ✅ 完整的Swagger API文档
- ✅ 批量数据导入功能
- ✅ 多级查询和统计功能

系统已完成开发并可正常运行，支持169,595条地址数据的高效管理和查询。

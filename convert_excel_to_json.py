#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel转JSON工具
将location_data.xlsx.remove.xlsx文件的三个sheet转换为JSON格式
"""

import json
import sys
from datetime import datetime
from openpyxl import load_workbook

def convert_value(value):
    """转换单元格值为合适的JSON格式"""
    if value is None:
        return None
    elif isinstance(value, datetime):
        return value.strftime('%Y-%m-%d %H:%M:%S')
    else:
        return value

def sheet_to_json(worksheet, output_file):
    """将worksheet转换为JSON格式并保存"""
    # 获取表头
    headers = []
    for cell in worksheet[1]:  # 第一行作为表头
        headers.append(cell.value)

    # 转换数据
    data = []
    for row in worksheet.iter_rows(min_row=2, values_only=True):  # 从第二行开始
        record = {}
        for i, value in enumerate(row):
            if i < len(headers):
                record[headers[i]] = convert_value(value)
        data.append(record)

    # 保存为JSON文件，每行一条记录
    with open(output_file, 'w', encoding='utf-8') as f:
        for record in data:
            f.write(json.dumps(record, ensure_ascii=False) + '\n')

    return data, headers

def convert_excel_to_json():
    """将Excel文件转换为JSON格式"""

    # Excel文件路径
    excel_file = "location_data.xlsx.remove.xlsx"

    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_file}")
        workbook = load_workbook(excel_file, read_only=True)

        # 获取所有sheet名称
        sheet_names = workbook.sheetnames
        print(f"发现 {len(sheet_names)} 个sheet: {sheet_names}")

        # 处理Sheet1 - 国家列表
        print("处理Sheet1 - 国家列表...")
        sheet1 = workbook[sheet_names[0]]
        country_data, country_headers = sheet_to_json(sheet1, 'location_data_country.json')
        print(f"国家数据已保存到 location_data_country.json，共 {len(country_data)} 条记录")

        # 处理Sheet2 - 省级行政区划列表
        if len(sheet_names) > 1:
            print("处理Sheet2 - 省级行政区划列表...")
            sheet2 = workbook[sheet_names[1]]
            province_data, province_headers = sheet_to_json(sheet2, 'location_data_province.json')
            print(f"省级数据已保存到 location_data_province.json，共 {len(province_data)} 条记录")
        else:
            print("警告: 没有找到Sheet2")
            province_headers = []

        # 处理Sheet3 - 市级行政区划列表
        if len(sheet_names) > 2:
            print("处理Sheet3 - 市级行政区划列表...")
            sheet3 = workbook[sheet_names[2]]
            city_data, city_headers = sheet_to_json(sheet3, 'location_data_city.json')
            print(f"市级数据已保存到 location_data_city.json，共 {len(city_data)} 条记录")
        else:
            print("警告: 没有找到Sheet3")
            city_headers = []

        workbook.close()

        print("\n转换完成！生成的文件：")
        print("- location_data_country.json")
        if len(sheet_names) > 1:
            print("- location_data_province.json")
        if len(sheet_names) > 2:
            print("- location_data_city.json")

        # 显示每个sheet的列信息
        print("\n各Sheet的列信息：")
        print("Sheet1 (国家列表) 列名:", country_headers)
        if len(sheet_names) > 1:
            print("Sheet2 (省级行政区划) 列名:", province_headers)
        if len(sheet_names) > 2:
            print("Sheet3 (市级行政区划) 列名:", city_headers)

    except FileNotFoundError:
        print(f"错误: 找不到文件 {excel_file}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    convert_excel_to_json()

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cashop.address</groupId>
        <artifactId>address</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>address-service</artifactId>
    <packaging>jar</packaging>

    <name>address-service</name>
    <description>地址管理服务业务层</description>

    <dependencies>
        <!-- 依赖common模块 -->
        <dependency>
            <groupId>com.cashop.address</groupId>
            <artifactId>address-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- 依赖dao模块 -->
        <dependency>
            <groupId>com.cashop.address</groupId>
            <artifactId>address-dao</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Spring IOC基础包 -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>

        <!-- Spring JDBC -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
        </dependency>

        <!-- 日志框架 -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>

<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cashop.base</groupId>
        <artifactId>base-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <groupId>com.cashop.address</groupId>
    <artifactId>address</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>address</name>
    <description>地址管理服务</description>

    <modules>
        <module>address-common</module>
        <module>address-dao</module>
        <module>address-facade</module>
        <module>address-service</module>
        <module>address-web</module>
    </modules>

    <properties>

        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

    </properties>


    <build>
        
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <release>${maven.compiler.source}</release>
                    <attach>true</attach>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <charset>${project.build.sourceEncoding}</charset>
                    <docencoding>${project.build.sourceEncoding}</docencoding>
                    <failOnError>false</failOnError>
                    <failOnWarnings>false</failOnWarnings>
                    <quiet>true</quiet>
                </configuration>
            </plugin>

        </plugins>
    </build>

    
</project>

package com.cashop.address.web.controller;

import com.cashop.address.common.entity.Address;
import com.cashop.address.service.AddressService;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地址管理控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/address")
@Tag(name = "地址管理", description = "地址管理相关接口，包括国家、省、市三级地址查询")
public class AddressController {

    private static final Logger logger = LoggerFactory.getLogger(AddressController.class);

    @Autowired
    private AddressService addressService;

    @Operation(summary = "初始化地址数据", description = "从JSON文件中读取地址数据并导入数据库（注意：需要先手动创建address表）")
    @PostMapping("/init")
    public Result<String> initAddressData() {
        try {
            String message = addressService.initAddressData();
            return Result.success(message);
        } catch (Exception e) {
            logger.error("初始化地址数据失败", e);
            return Result.error("初始化失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询所有国家列表", description = "获取所有可用的国家列表")
    @GetMapping("/countries")
    public Result<List<Address>> getAllCountries() {
        try {
            List<Address> countries = addressService.getAllCountries();
            return Result.success(countries);
        } catch (Exception e) {
            logger.error("查询国家列表失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据国家编码查询省级行政区划列表", description = "根据指定的国家编码查询该国家下的所有省/州")
    @GetMapping("/provinces")
    public Result<List<Address>> getProvincesByCountryCode(
            @Parameter(description = "国家编码", required = true, example = "US")
            @RequestParam("countryCode") String countryCode) {
        try {
            List<Address> provinces = addressService.getProvincesByCountryCode(countryCode);
            return Result.success(provinces);
        } catch (Exception e) {
            logger.error("查询省级行政区划失败，国家编码: {}", countryCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据省编码查询市级行政区划列表", description = "根据指定的省编码查询该省下的所有市/区")
    @GetMapping("/cities")
    public Result<List<Address>> getCitiesByProvinceCode(
            @Parameter(description = "省编码", required = true, example = "3907")
            @RequestParam("provinceCode") String provinceCode) {
        try {
            List<Address> cities = addressService.getCitiesByProvinceCode(provinceCode);
            return Result.success(cities);
        } catch (Exception e) {
            logger.error("查询市级行政区划失败，省编码: {}", provinceCode, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "根据编码查询地址信息", description = "根据地址编码查询具体的地址信息")
    @GetMapping("/detail")
    public Result<Address> getAddressByCode(
            @Parameter(description = "地址编码", required = true, example = "US")
            @RequestParam("code") String code) {
        try {
            Address address = addressService.getAddressByCode(code);
            if (address != null) {
                return Result.success(address);
            } else {
                return Result.error("未找到对应的地址信息");
            }
        } catch (Exception e) {
            logger.error("查询地址详情失败，编码: {}", code, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "统计地址数量", description = "统计指定级别和国家的地址数量")
    @GetMapping("/count")
    public Result<Long> countAddresses(
            @Parameter(description = "地址级别：1-国家，2-省，3-市", required = true, example = "1")
            @RequestParam("level") Integer level,
            @Parameter(description = "国家编码（可选）", example = "US")
            @RequestParam(value = "countryCode", required = false) String countryCode) {
        try {
            long count = addressService.countAddresses(level, countryCode);
            return Result.success(count);
        } catch (Exception e) {
            logger.error("统计地址数量失败，级别: {}, 国家编码: {}", level, countryCode, e);
            return Result.error("统计失败: " + e.getMessage());
        }
    }
}

package com.cashop.address.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * 地址管理服务启动类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = "com.cashop.address")
@EnableDiscoveryClient
@MapperScan("com.cashop.address.dao.mapper")
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

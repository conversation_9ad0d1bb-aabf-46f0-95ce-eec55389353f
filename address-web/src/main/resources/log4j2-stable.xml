<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS}[%-5level][%t][%logger{36}][%L] - %msg%n</Property>
        <Property name="LOG_PATH">/usr/local/tomcat/logs</Property>
    </Properties>

    <Appenders>
        <!-- 文件输出 -->
        <RollingFile name="RollingFile" fileName="${LOG_PATH}/catalina.out"
                     filePattern="${LOG_PATH}/catalina-%d{yyyy-MM-dd}.out">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- 公司包日志级别 -->
        <Logger name="com.cashop" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>
        <Logger name="com.mengxiang" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>
        <Logger name="com.aikucun" level="INFO" additivity="false">
            <AppenderRef ref="RollingFile"/>
        </Logger>

        <!-- 根日志级别 -->
        <Root level="WARN">
            <AppenderRef ref="RollingFile"/>
        </Root>
    </Loggers>
</Configuration>

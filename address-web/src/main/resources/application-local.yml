spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************************************************************
    username: stable78
    password: T1cMe8U183
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: AddressHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.cashop.address.common.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000

eureka:
  client:
    service-url:
      defaultZone: http://merchant:<EMAIL>:8080/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${server.port}
    prefer-ip-address: true

apollo:
  cluster: stable
  meta: http://apollo.akcrelease.com:8080
